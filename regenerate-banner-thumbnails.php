<?php
/**
 * Regenerate thumbnails for banner images
 * Run this script once to generate the new medium_banner size for existing images
 * 
 * Usage: Place this file in the WordPress root directory and run via browser or CLI
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!function_exists('wp_generate_attachment_metadata')) {
    require_once(ABSPATH . 'wp-admin/includes/image.php');
}

echo "<h2>Regenerating Banner Image Thumbnails</h2>\n";
echo "<p>Adding medium_banner size (1200x800) to existing images...</p>\n";

// Get all image attachments
$images = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'post_status' => 'inherit',
    'posts_per_page' => -1
));

$processed = 0;
$errors = 0;

foreach ($images as $image) {
    $file_path = get_attached_file($image->ID);
    
    if (!file_exists($file_path)) {
        echo "Skipping {$image->post_title} - file not found<br>\n";
        $errors++;
        continue;
    }
    
    // Generate new metadata (this will create missing thumbnail sizes)
    $metadata = wp_generate_attachment_metadata($image->ID, $file_path);
    
    if ($metadata) {
        wp_update_attachment_metadata($image->ID, $metadata);
        echo "Processed: {$image->post_title}<br>\n";
        $processed++;
    } else {
        echo "Error processing: {$image->post_title}<br>\n";
        $errors++;
    }
    
    // Flush output for real-time feedback
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

echo "<h3>Complete!</h3>\n";
echo "<p>Processed: {$processed} images</p>\n";
echo "<p>Errors: {$errors} images</p>\n";
echo "<p>You can now delete this file.</p>\n";
?>
