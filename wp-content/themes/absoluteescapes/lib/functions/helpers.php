<?php

/**
 *  Convert Hex to RGB
 */
function hex2rgb($hex)
{
    $hex = str_replace("#", "", $hex);

    if (strlen($hex) == 3) {
        $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
        $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
        $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
    } else {
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
    }
    $rgb = array($r, $g, $b);
    //return implode(",", $rgb); // returns the rgb values separated by commas
    return $rgb; // returns an array with the rgb values
}



/**
 * Format string to url
 */
function seoUrl($string)
{
    //Lower case everything
    $string = strtolower($string);
    //Make alphanumeric (removes all other characters)
    $string = preg_replace("/[^a-z0-9_\s-]/", "", $string);
    //Clean up multiple dashes or whitespaces
    $string = preg_replace("/[\s-]+/", " ", $string);
    //Convert whitespaces and underscore to dash
    $string = preg_replace("/[\s_]/", "-", $string);
    return $string;
}


/**
 * Converts integer into words
 */
function convert_number_to_words($number)
{

    $hyphen = '-';
    $conjunction = ' and ';
    $separator = ', ';
    $negative = 'negative ';
    $decimal = ' point ';
    $dictionary = array(
        0 => 'zero',
        1 => 'one',
        2 => 'two',
        3 => 'three',
        4 => 'four',
        5 => 'five',
        6 => 'six',
        7 => 'seven',
        8 => 'eight',
        9 => 'nine',
        10 => 'ten',
        11 => 'eleven',
        12 => 'twelve',
        13 => 'thirteen',
        14 => 'fourteen',
        15 => 'fifteen',
        16 => 'sixteen',
        17 => 'seventeen',
        18 => 'eighteen',
        19 => 'nineteen',
        20 => 'twenty',
        30 => 'thirty',
        40 => 'fourty',
        50 => 'fifty',
        60 => 'sixty',
        70 => 'seventy',
        80 => 'eighty',
        90 => 'ninety',
        100 => 'hundred',
        1000 => 'thousand',
        1000000 => 'million',
        1000000000 => 'billion',
        1000000000000 => 'trillion',
        1000000000000000 => 'quadrillion',
        1000000000000000000 => 'quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int)$number < 0) || (int)$number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens = ((int)($number / 10)) * 10;
            $units = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int)($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string)$fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}



/**
 * Wrapper to pass variable to template part
 *
 * @param string $file
 * @param object|array $var
 * @param string $suffix
 */
function get_component($file, $var = [], $suffix = '')
{

    if ($var) {
        set_query_var('data', $var);
    }

    get_template_part('lib/components/' . $file, $suffix);

    if ($var) {
        set_query_var('data', '');
    }
}

function getSrcSet($id) {

    // Credit: https://gist.github.com/joshuadavidnelson/eb4650aa1ee8da9c7d731960e9402e21

    $img_srcset   = wp_get_attachment_image_srcset($id, 'full');
    $srcset_array = explode(", ", $img_srcset);
    $images  = array();
    $x = 0;
  
    foreach ($srcset_array as $set) :
  
      $split = explode(" ",$set );
  
      if (!isset($split[0], $split[1])) continue;
  
      $images[$x]['src'] = $split[0];
      $images[$x]['width'] = str_replace('w', '', $split[1]);
  
      $x++;
  
    endforeach;
    
    // sort the array, ordered by width
    usort($images, function($a, $b) {
      return $a['width'] <=> $b['width'];
    });

    // Offset values down by one, to effectively increase the image resolution used.
    // This is a quick amd simple way to improve image quality on HiDPI devices.

    // Modify the last value to remove resolution suffix (so it points to original image)
    $original = [
        'src' => preg_replace('/-\d+x\d+/', '', end($images)['src']),
        'width' => end($images)['width']
    ];

    // Get the total number of elements in the array
    $total_elements = count($images);

    // Get the original 'src' value from the last element before it's shifted
    $last_src = $images[$total_elements - 1]['src'];

    // Remove the pattern (e.g., -100x100) from the 'src' value of the last element
    $original_src = preg_replace('/-\d+x\d+/', '', $last_src);

    // Shift the 'src' values down by one key. Starting key is $start

    $start = 2;
    for ($i = $start; $i < $total_elements - 1; $i++) {
        $images[$i]['src'] = $images[$i + 1]['src'];
    }

    // Set the modified 'src' value in the last element
    $images[$total_elements - 1]['src'] = $original_src;

    // write_log($images);

    return $images;
  }


function outputSrcset($image_id, $selector) {

    $css = '';
    $srcset = getSrcSet($image_id);

    $css .= ":root {--base64-image: url('".getBase64(wp_get_attachment_image_url($image_id, 'tiny'))."');}\n";

    foreach ($srcset as $set) :

        // skip big ones
        // if ($set['width'] > 1600) continue;

        $css .= "@media only screen and (min-width: " . $set["width"] . "px) {
        ". $selector ." { background-image: url(" . $set["src"] . "),var(--base64-image) } 
        }
        ";

    endforeach;

    // Use full size for tablet/medium screens (550px to 955px) - highest quality
    $full_image = wp_get_attachment_image_url($image_id, 'full');
    $css .= "@media only screen and (min-width: 550px) and (max-width: 955px) {
        ". $selector ." { background-image: url(" . $full_image . "),var(--base64-image) !important }
        }
        ";

    // Use small size only for very small portrait screens (350px to 549px)
    $mobile_small = wp_get_attachment_image_url($image_id, 'small');
    $css .= "@media only screen and (min-width: 350px) and (max-width: 549px) and (orientation: portrait) {
        ". $selector ." { background-image: url(" . $mobile_small . "),var(--base64-image) }
        }
        ";

    $css = (!empty($css)) ? '<style>' . $css . '</style>' : ''; 
    echo $css;

}

function getBase64($url) {
    require_once ABSPATH . 'wp-admin/includes/file.php';
    $local_path = rtrim(get_home_path(),'/').'/'.ltrim(parse_url($url)['path'],'/');
    // write_log($local_path);
    $parts = explode('.',$local_path);
    $type = $parts[count($parts)-1];
    $prefix = 'data:image/'.$type.';base64,';
    return $prefix. base64_encode(file_get_contents($local_path));
}

/**
 * Filter holidays in ACF post object field to only show holidays from current taxonomy
 */
function acf_filter_featured_holidays_by_current_taxonomy($args, $field, $post_id) {
    // Only apply this filter to our specific field
    if ($field['name'] !== 'holiday') {
        return $args;
    }

    // Only apply in admin
    if (!is_admin()) {
        return $args;
    }

    // Get current screen info
    $screen = get_current_screen();

    // Get taxonomy and term ID from various possible URL structures
    $taxonomy = null;
    $term_id = 0;

    // Check for term.php?taxonomy=X&tag_ID=Y (term edit page)
    if (isset($_GET['taxonomy']) && isset($_GET['tag_ID'])) {
        $taxonomy = $_GET['taxonomy'];
        $term_id = intval($_GET['tag_ID']);
    }
    // Also check screen base for term editing
    elseif ($screen && $screen->base === 'term' && isset($_GET['taxonomy']) && isset($_GET['tag_ID'])) {
        $taxonomy = $_GET['taxonomy'];
        $term_id = intval($_GET['tag_ID']);
    }

    // If we don't have both taxonomy and term_id, don't filter
    if (!$taxonomy || !$term_id) {
        return $args;
    }

    // Debug logging - always log for now to troubleshoot
    error_log("ACF Holiday Filter Debug: Screen = " . ($screen ? $screen->base : 'unknown') . ", Taxonomy = $taxonomy, Term ID = $term_id");
    error_log("ACF Holiday Filter Debug: Field name = " . $field['name'] . ", Field key = " . $field['key']);

    // Handle holiday-type taxonomy
    if ($taxonomy === 'holiday-type') {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'holiday-type',
                'field'    => 'term_id',
                'terms'    => $term_id,
            ),
        );
        error_log("ACF Holiday Filter: Applied holiday-type filter for term $term_id");
    }

    // Handle holiday-regions taxonomy
    if ($taxonomy === 'holiday-regions') {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'holiday-regions',
                'field'    => 'term_id',
                'terms'    => $term_id,
            ),
        );
        error_log("ACF Holiday Filter: Applied holiday-regions filter for term $term_id");
    }

    // Log the final args for debugging
    error_log("ACF Holiday Filter: Final args = " . print_r($args, true));

    return $args;
}
add_filter('acf/fields/post_object/query/name=holiday', 'acf_filter_featured_holidays_by_current_taxonomy', 10, 3);

/**
 * Alternative approach - filter by field key for holiday regions
 */
function acf_filter_holiday_regions_featured_holidays($args, $field, $post_id) {
    // Only apply to the specific field key for holiday regions
    if ($field['key'] !== 'field_675c3014f2014') {
        return $args;
    }

    // Get the current term being edited
    if (isset($_GET['tag_ID']) && isset($_GET['taxonomy']) && $_GET['taxonomy'] === 'holiday-regions') {
        $term_id = intval($_GET['tag_ID']);

        if ($term_id) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'holiday-regions',
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            );

            error_log("Holiday Regions Filter: Applied filter for term $term_id");
        }
    }

    return $args;
}
add_filter('acf/fields/post_object/query/key=field_675c3014f2014', 'acf_filter_holiday_regions_featured_holidays', 10, 3);

/**
 * Alternative approach - filter by field key for holiday types
 */
function acf_filter_holiday_types_featured_holidays($args, $field, $post_id) {
    // Only apply to the specific field key for holiday types
    if ($field['key'] !== 'field_675c2004f1238') {
        return $args;
    }

    // Get the current term being edited
    if (isset($_GET['tag_ID']) && isset($_GET['taxonomy']) && $_GET['taxonomy'] === 'holiday-type') {
        $term_id = intval($_GET['tag_ID']);

        if ($term_id) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'holiday-type',
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            );

            error_log("Holiday Types Filter: Applied filter for term $term_id");
        }
    }

    return $args;
}
add_filter('acf/fields/post_object/query/key=field_675c2004f1238', 'acf_filter_holiday_types_featured_holidays', 10, 3);

/**
 * Alternative approach using acf/load_field to modify the field choices
 */
function acf_load_filtered_holidays_field($field) {
    // Always log to see if this function is being called
    error_log("ACF Load Field: Field name = " . $field['name'] . ", Field key = " . $field['key']);

    // Only apply to holiday post object fields in our featured holidays
    if ($field['name'] !== 'holiday' || $field['type'] !== 'post_object') {
        return $field;
    }

    // Check if we're on a taxonomy edit page
    if (!is_admin() || !isset($_GET['taxonomy']) || !isset($_GET['tag_ID'])) {
        return $field;
    }

    $taxonomy = $_GET['taxonomy'];
    $term_id = intval($_GET['tag_ID']);

    error_log("ACF Load Field: Detected taxonomy = $taxonomy, term_id = $term_id");

    // Only filter for our supported taxonomies
    if (!in_array($taxonomy, ['holiday-type', 'holiday-regions'])) {
        return $field;
    }

    // Get holidays for this taxonomy term
    $holidays = get_posts(array(
        'post_type' => 'holiday',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'tax_query' => array(
            array(
                'taxonomy' => $taxonomy,
                'field'    => 'term_id',
                'terms'    => $term_id,
            ),
        ),
    ));

    error_log("ACF Load Field: Found " . count($holidays) . " holidays for $taxonomy term $term_id");

    // If we found holidays, modify the field to only show these
    if (!empty($holidays)) {
        $holiday_ids = array_map(function($holiday) {
            return $holiday->ID;
        }, $holidays);

        // Modify the field to include a taxonomy filter
        if (!isset($field['taxonomy'])) {
            $field['taxonomy'] = array();
        }

        // Set the taxonomy filter for this field
        $field['taxonomy'] = $taxonomy . ':' . $term_id;

        error_log("ACF Load Field: Limiting to holiday IDs: " . implode(', ', $holiday_ids));
    }

    return $field;
}

// Try multiple hooks to catch the field loading
add_filter('acf/load_field/name=holiday', 'acf_load_filtered_holidays_field');
add_filter('acf/load_field/key=field_675c3014f2014', 'acf_load_filtered_holidays_field'); // Holiday regions
add_filter('acf/load_field/key=field_675c2004f1238', 'acf_load_filtered_holidays_field'); // Holiday types

/**
 * Filter ACF AJAX queries for post object fields
 */
function acf_filter_ajax_query_for_holidays($query) {
    // Only apply during AJAX requests
    if (!wp_doing_ajax()) {
        return;
    }

    // Check if this is an ACF query for holidays
    if (!isset($_POST['action']) || $_POST['action'] !== 'acf/fields/post_object/query') {
        return;
    }

    // Check if we have the field key in the request
    if (!isset($_POST['field_key'])) {
        return;
    }

    $field_key = $_POST['field_key'];

    // Log the AJAX request
    error_log("ACF AJAX Query: Field key = $field_key");

    // Check if this is one of our holiday fields
    if (!in_array($field_key, ['field_675c3014f2014', 'field_675c2004f1238'])) {
        return;
    }

    // Get taxonomy info from referer URL
    $referer = wp_get_referer();
    if (!$referer) {
        return;
    }

    $parsed_url = parse_url($referer);
    parse_str($parsed_url['query'], $query_params);

    if (!isset($query_params['taxonomy']) || !isset($query_params['tag_ID'])) {
        return;
    }

    $taxonomy = $query_params['taxonomy'];
    $term_id = intval($query_params['tag_ID']);

    error_log("ACF AJAX Query: Filtering for $taxonomy term $term_id");

    // Apply the taxonomy filter
    $query->set('tax_query', array(
        array(
            'taxonomy' => $taxonomy,
            'field'    => 'term_id',
            'terms'    => $term_id,
        ),
    ));
}
add_action('pre_get_posts', 'acf_filter_ajax_query_for_holidays');